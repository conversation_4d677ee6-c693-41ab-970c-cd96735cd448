#include "main_app.h"
#include "MyARGB/MyARGB.h"

extern TIM_HandleTypeDef htim2;
extern DMA_HandleTypeDef hdma_tim2_ch2_ch4;
extern DMA_HandleTypeDef hdma_tim2_ch1;

_Noreturn void main_app(void)
{
	  ARGB Strip1;
	  ARGB_New(&Strip1, 14, 2, TIM_CHANNEL_2, &htim2, &hdma_tim2_ch2_ch4, BYTE);
	  ARGB_Init(&Strip1);

	  ARGB Strip2;
	  ARGB_New(&Strip2, 14, 2, TIM_CHANNEL_1, &htim2, &hdma_tim2_ch1, BYTE);
	  ARGB_Init(&Strip2);

	  ARGB_Clear(&Strip1);
	  ARGB_Clear(&Strip2);
	  ARGB_Show(&Strip1);
	  ARGB_Show(&Strip2);

	  while(1)
	  {
		  ARGB_SetRGB(&Strip1, 0, 255, 0, 0);
		  ARGB_SetRGB(&Strip1, 1, 0, 255, 0);
		  ARGB_SetRGB(&Strip1, 2, 0, 0, 255);
		  ARGB_Show(&Strip1);
//
		  ARGB_SetRGB(&Strip2, 0, 0, 0, 255);
		  ARGB_SetRGB(&Strip2, 1, 0, 255, 0);
		  ARGB_SetRGB(&Strip2, 2, 255, 0, 0);
		  ARGB_Show(&Strip2);
	  }

}
