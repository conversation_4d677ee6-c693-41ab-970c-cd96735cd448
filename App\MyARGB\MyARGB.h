#ifndef MYARGB_H_
#define MYARGB_H_

#include "math.h"
#include "libs.h"

#define MAX_ARGB_INSTANCES 2
#define USE_GAMMA_CORRECTION 0 // todo Пока убрал
#define MAX_PWM_BUF_SIZE 64    // Максимальный размер PWM буфера (для SK6812: 4*8*2 = 64)

typedef void (*PWM_Write_Func)(void* buffer, uint32_t index, uint32_t value);
typedef uint32_t (*PWM_Read_Func)(void* buffer, uint32_t index);

typedef enum {WS2811S, WS2811F, WS2812, SK6812} t_Chip;
typedef enum {APB1, APB2} t_APB;
typedef enum {BYTE, HWORD, WORD} t_DMA_Size;

typedef enum ARGB_STATE {
    ARGB_BUSY = 0,  ///< DMA Transfer in progress
    ARGB_READY = 1, ///< DMA Ready to transfer
    ARGB_OK = 2,    ///< Function execution success
    ARGB_PARAM_ERR = 3, ///< Error in input parameters
} ARGB_STATE;

typedef struct ARGB
{
		uint16_t NumPixels;

		uint16_t TimNum;
		TIM_HandleTypeDef *TimHandler;

		uint8_t  TimCH;     // Todo Создать свой тип данных, чтобы обеспечить защиту от дураков
		uint16_t TIM_DMA_ID;
		uint32_t TIM_DMA_CC;
		volatile uint32_t *TIM_CCR;

		DMA_HandleTypeDef *DMAHandler;

		t_APB APB;
		t_DMA_Size DMA_Size;

	    uint8_t PWM_BUF[MAX_PWM_BUF_SIZE];  // Статический массив PWM буфера
	    PWM_Write_Func PWM_Write;
	    PWM_Read_Func PWM_Read;

		uint64_t PWM_BUF_LEN;
		volatile u16_t BUF_COUNTER;// = 0;
		volatile u8_t PWM_HI;
		volatile u8_t PWM_LO;

		volatile u8_t *RGB_BUF;

		volatile u8_t ARGB_BR;// = 255;
		volatile ARGB_STATE ARGB_LOC_ST;

}ARGB;

void ARGB_New(ARGB *this, uint16_t NumPixels, uint16_t TimNum, int8_t  TimCH, TIM_HandleTypeDef *TimHandler, DMA_HandleTypeDef *DMAHandler, t_DMA_Size DMA_Size);
void ARGB_Init(ARGB *this);
void ARGB_Clear(ARGB *this);
void ARGB_SetRGB(ARGB *this, u16_t i, u8_t r, u8_t g, u8_t b);
void ARGB_FillRGB(ARGB *this, u8_t r, u8_t g, u8_t b);
ARGB_STATE ARGB_Show(ARGB *this);

#endif
